local PlayerDataManager = require(script.Parent.PlayerDataLoader)

local function awardPoints(player, points)
    local playerData = PlayerDataManager:GetPlayerData(player)
    if playerData then
        local newScore = playerData.score + points
        PlayerDataManager:UpdatePlayerData(player, {score = newScore})
        
        local leaderstats = player:FindFirstChild("leaderstats")
        if leaderstats and leaderstats:FindFirstChild("Score") then
            leaderstats.Score.Value = newScore
        end
    end
end

local function levelUpPlayer(player)
    local playerData = PlayerDataManager:GetPlayerData(player)
    if playerData then
        local newLevel = playerData.level + 1
        PlayerDataManager:UpdatePlayerData(player, {level = newLevel})
        
        local leaderstats = player:FindFirstChild("leaderstats")
        if leaderstats and leaderstats:FindFirstChild("Level") then
            leaderstats.Level.Value = newLevel
        end
    end
end

local function addItemToInventory(player, item)
    local playerData = PlayerDataManager:GetPlayerData(player)
    if playerData then
        table.insert(playerData.inventory, item)
        PlayerDataManager:UpdatePlayerData(player, {inventory = playerData.inventory})
    end
end

local function banPlayer(adminPlayer, targetPlayerName)
    local targetPlayer = game.Players:FindFirstChild(targetPlayerName)
    if targetPlayer then
        PlayerDataManager:SetPlayerBanStatus(targetPlayer, true)
    end
end

local function unbanPlayer(adminPlayer, targetPlayerName)
    local targetPlayer = game.Players:FindFirstChild(targetPlayerName)
    if targetPlayer then
        PlayerDataManager:SetPlayerBanStatus(targetPlayer, false)
    end
end

local function setupPointsPart(part, points)
    local debounce = {}
    
    part.Touched:Connect(function(hit)
        local humanoid = hit.Parent:FindFirstChild("Humanoid")
        if humanoid then
            local player = game.Players:GetPlayerFromCharacter(hit.Parent)
            if player and not debounce[player] then
                debounce[player] = true
                awardPoints(player, points)
                
                wait(3)
                debounce[player] = nil
            end
        end
    end)
end

return {
    awardPoints = awardPoints,
    levelUpPlayer = levelUpPlayer,
    addItemToInventory = addItemToInventory,
    banPlayer = banPlayer,
    unbanPlayer = unbanPlayer,
    setupPointsPart = setupPointsPart
}
