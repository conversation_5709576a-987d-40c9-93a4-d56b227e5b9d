local HttpService = game:GetService("HttpService")

local API_BASE_URL = "https://api-testing-iota-one.vercel.app"
local GAME_ID = game.GameID

local PlayerDataManager = {}
PlayerDataManager.LoadedPlayers = {}

local DEFAULT_PLAYER_DATA = {
    level = 1,
    score = 0,
    coins = 100,
    inventory = {},
    settings = {music = true, sfx = true},
    IsBanned = false
}

local function makeAPIRequest(method, endpoint, data)
    local url = API_BASE_URL .. endpoint
    local success, result = pcall(function()
        if method == "GET" then
            return HttpService:GetAsync(url)
        elseif method == "POST" then
            return HttpService:PostAsync(url, HttpService:JSONEncode(data), Enum.HttpContentType.ApplicationJson)
        elseif method == "PUT" then
            return HttpService:RequestAsync({
                Url = url,
                Method = "PUT",
                Headers = {["Content-Type"] = "application/json"},
                Body = HttpService:JSONEncode(data)
            }).Body
        elseif method == "DELETE" then
            return HttpService:RequestAsync({
                Url = url,
                Method = "DELETE"
            }).Body
        end
    end)
    
    if success then
        return HttpService:JSONDecode(result)
    else
        warn("API Request failed: " .. tostring(result))
        return nil
    end
end

function PlayerDataManager:LoadPlayerData(player)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("GET", endpoint)
    
    if response and response.success then
        local playerData = response.data
        if playerData.IsBanned then
            player:Kick("You have been banned from this game.")
            return nil
        end
        self.LoadedPlayers[userId] = playerData
        return playerData
    else
        return self:CreateDefaultPlayerData(player)
    end
end

function PlayerDataManager:CreateDefaultPlayerData(player)
    local userId = tostring(player.UserId)
    local defaultData = {}
    
    for key, value in pairs(DEFAULT_PLAYER_DATA) do
        if type(value) == "table" then
            defaultData[key] = {}
            for k, v in pairs(value) do
                defaultData[key][k] = v
            end
        else
            defaultData[key] = value
        end
    end
    
    local success = self:SavePlayerData(player, defaultData)
    if success then
        self.LoadedPlayers[userId] = defaultData
        return defaultData
    else
        warn("Failed to create default data for " .. player.Name)
        return nil
    end
end

function PlayerDataManager:SavePlayerData(player, data)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("POST", endpoint, data)
    
    if response and response.success then
        return true
    else
        warn("Failed to save data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:UpdatePlayerData(player, updates)
    local userId = tostring(player.UserId)
    
    if not self.LoadedPlayers[userId] then
        warn("Player data not loaded for " .. player.Name)
        return false
    end
    
    for key, value in pairs(updates) do
        self.LoadedPlayers[userId][key] = value
    end
    
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("PUT", endpoint, updates)
    
    if response and response.success then
        return true
    else
        warn("Failed to update data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:GetPlayerData(player)
    local userId = tostring(player.UserId)
    return self.LoadedPlayers[userId]
end

function PlayerDataManager:SetPlayerBanStatus(player, isBanned)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("PUT", endpoint, {IsBanned = isBanned})
    
    if response and response.success then
        if isBanned then
            player:Kick("You have been banned from this game.")
        end
        return true
    else
        warn("Failed to " .. (isBanned and "ban" or "unban") .. " player: " .. player.Name)
        return false
    end
end

function PlayerDataManager:CleanupPlayer(player)
    local userId = tostring(player.UserId)
    if self.LoadedPlayers[userId] then
        self.LoadedPlayers[userId] = nil
    end
end

game["Players"].PlayerAdded:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:LoadPlayerData(Player)
        
        if playerData then
            local leaderstats = Instance.new("Folder")
            leaderstats.Name = "leaderstats"
            leaderstats.Parent = Player
            
            local level = Instance.new("IntValue")
            level.Name = "Level"
            level.Value = playerData.level
            level.Parent = leaderstats
            
            local score = Instance.new("IntValue")
            score.Name = "Score"
            score.Value = playerData.score
            score.Parent = leaderstats
        end
    end)
end)

game["Players"].PlayerRemoving:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:GetPlayerData(Player)
        if playerData then
            local leaderstats = Player:FindFirstChild("leaderstats")
            if leaderstats then
                local level = leaderstats:FindFirstChild("Level")
                local score = leaderstats:FindFirstChild("Score")
                
                if level then playerData.level = level.Value end
                if score then playerData.score = score.Value end
                
                PlayerDataManager:SavePlayerData(Player, playerData)
            end
        end
        
        PlayerDataManager:CleanupPlayer(Player)
    end)
end)

return PlayerDataManager
